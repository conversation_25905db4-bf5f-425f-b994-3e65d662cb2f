curl https://api.mistral.ai/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bear<PERSON> hKCPaWJP2ZfMjODrwu5RWKiAzfkTfEXP" \
  -d '{
    "model": "pixtral-12b-2409",
    "messages": [
      {
        "role": "system",
        "content": {
          "type": "text",
          "text": "You are a helpful assistant that provides concise and descriptive explanations of images. Focus on identifying key elements and settings in the image."
        }
      },
      {
        "role": "user",
        "content": [
          {
            "type": "text",
            "text": "What’s in this image?"
          },
          {
            "type": "image_url",
            "image_url": "https://firebasestorage.googleapis.com/v0/b/project-report-f7120.appspot.com/o/newwways-testing-mnpk3%2F9cb1697b-06a8-4523-801c-79539b000000%2F6_k0sv5ws2.jpg?alt=media&token=bcb05bbe-c2f0-4b4b-8b29-6f51537e2e6d"
          }
        ]
      },
      {
        "role": "assistant",
        "content": {
          "type": "text",
          "text": "This image appears to show a peaceful outdoor scene with a lake and mountains in the background. The colors are vibrant, and the sky is clear."
        }
      },
    ],
    "max_tokens": 300
  }'

  // https://firebasestorage.googleapis.com/v0/b/project-report-f7120.appspot.com/o/newwways-testing-mnpk3%2F9cb1697b-06a8-4523-801c-79539b000000%2F6_k0sv5ws2.jpg?alt=media&token=bcb05bbe-c2f0-4b4b-8b29-6f51537e2e6d

