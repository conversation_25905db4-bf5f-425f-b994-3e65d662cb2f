const express = require("express")
const cors = require("cors")
const app = express()

app.use(cors()) // Aktiviert CORS für alle Routen
app.use(express.json()) // Um JSON-Body aus Requests zu parsen

// Import Auth Data
process.env.GOOGLE_APPLICATION_CREDENTIALS = "project-report-f7120-9dda32ea8083.json"

/**
 * Hier fügen Sie Ihren bestehenden Code für die Google Vertex AI API ein
 */
const project = "project-report-f7120"
const theLocation = "europe-west3"
const aiplatform = require("@google-cloud/aiplatform")

// Imports the Google Cloud Prediction service client
const {PredictionServiceClient} = aiplatform.v1

// Import the helper module for converting arbitrary protobuf.Value objects.
const {helpers} = aiplatform

// Specifies the location of the api endpoint
const clientOptions = {
	apiEndpoint: "us-central1-aiplatform.googleapis.com",
}

const publisher = "google"
const model = "text-bison@001"

// Instantiates a client
const predictionServiceClient = new PredictionServiceClient(clientOptions)

// Ändern Sie die callPredict Funktion, um einen Prompt als Parameter zu akzeptieren
async function callPredict(userPrompt) {
	// ... (der Rest Ihres Codes bleibt gleich, ersetzen Sie nur die prompt Variable mit userPrompt)

	// Configure the parent resource
	const endpoint = `projects/${project}/locations/${theLocation}/publishers/${publisher}/models/${model}`

	const instanceValue = helpers.toValue(userPrompt)

	const instances = [instanceValue]

	const parameter = {
		temperature: 0.1,
		maxOutputTokens: 256,
		topP: 0.95,
		topK: 1,
	}
	const parameters = helpers.toValue(parameter)

	const request = {
		endpoint,
		instances,
		parameters,
	}

	// Predict request
	const response = await predictionServiceClient.predict(request)
	console.log("Get text prompt response")
	console.log(JSON.stringify(response[0].predictions[0].structValue.fields.content, null, 2))

	// Ändern Sie diesen Teil, um die Antwort zurückzugeben
	return response[0].predictions[0].structValue.fields.content // oder die entsprechende Struktur Ihrer Antwort
}

// Erstellen Sie einen POST-Endpoint
app.post("/predict", async (req, res) => {
	try {
		if (!req.body || !req.body.prompt) {
			return res.status(400).send({error: "No request body or wrong body format"})
		}
		const userPrompt = {
			prompt: req.body.prompt,
		}
		console.log("User prompt: ", userPrompt)
		const response = await callPredict(userPrompt)
		res.send(response) // Senden Sie die Antwort zurück an den Client
	} catch (error) {
		res.status(500).send({error: error.message})
	}
})

// Starten Sie den Server
const PORT = 6400
app.listen(PORT, () => {
	console.log(`Server läuft auf Port ${PORT}`)
})
