const express = require("express")
const cors = require("cors")
const axios = require("axios")
// import axios from "axios"


const app = express()

app.use(cors()) // Aktiviert CORS für alle Routen
app.use(express.json({limit: "50mb"})) // Um JSON-Body aus Requests zu parsen

// Konfiguration
require("dotenv").config()

/**
 * Open AI Setup
 */
// import OpenAI from "openai"
const OpenAI = require("openai")
const openAIApiKey = process.env.OPENAI_API_KEY
const mixtralPixtralApiKey = process.env.MIXTRAL_PIXTRAL_API_KEY

const openai = new OpenAI({
	apiKey: openAIApiKey,
	organization: "org-BLBnq9qzPbbWuPscJxEUdA5R",
})

async function processOpenAIStream(stream, res) {
	for await (const chunk of stream) {
		process.stdout.write(chunk.choices[0]?.delta?.content || "")
		let content = chunk.choices[0]?.delta?.content || ""
		// Verarbeiten Sie den Inhalt wie benötigt, z.B. senden an eine HTTP-Antwort
		res.write(content)
	}
	// Schließen Sie die Response, wenn der Stream endet
	res.end()
}

// TODO: Add Temperature etc to params
async function gptResponse(model, messagesObject) {
	// console.log("gptResponse called")
	// console.log("messagesObject: ", JSON.stringify(messagesObject))
	// console.log("-> body", body)
	// console.log("-> body.context ", body.context)
	// console.log("-> body.messages.content", body.messages[0].content)

	const requestObject = {
		// model: "gpt-4",
		model: model, // "gpt-4-turbo-preview",
		// model: "gpt-3.5-turbo-0125",
		// messages: [
		// 	{role: "system", content: body.context},
		// 	{role: "user", content: body.messages[0].content},
		// ],
		messages: messagesObject,
		temperature: 0,
		stream: true,
		stream_options: {include_usage: true},
	}

	// console.log("requestObject: ", requestObject)

	// TODO: change to "if(seedValue)" also, wenn ein seed Wert übergeben wurde, dann nutze ihn
	if (model === "gpt-4-1106-preview") {
		console.log('model === "gpt-4-1106-preview"')
		requestObject.seed = 123
	}

	const stream = await openai.chat.completions.create(requestObject)

	// create a for loop to read the stream
	// for await (const chunk of stream) {
	// 	console.log("chunk.choices: ", chunk.choices)
	// 	console.log("chunk.usage: ", chunk.usage)
	// }

	return stream

	// stream.on("end", () => {
	// 	res.end() // Schließt die Verbindung, wenn der Stream endet
	// })
}

/*
 * Image analysis
 *
 * Benchmark Frage: Was ist die richtige Muffenspaltweite für Rohre mit einem Spitzendurchmesser von 426?
 *
 */

const tableSystemPrompt = `Du analysierst Abbildungen und gibst eine prezise und beurteilst ob auf der Abbildung etwas zu sehen ist, dass mit der Frage des Nutzers zu tun hat. Wenn man beispielsweise eine Abbdilung sieht, die Ein Rohr zeigt und im mitgesendetem Kontext von Stoßfugenbegrenzungen gesprochen wird, dann ist die Abbildung relevant, wenn die Frage mit Stoßfugenbegrenzungen zu tun hat. 

Für die Analysie der Abbdildung ist es wichtig, dass Du den Kontext beachtest, der unten beigefügt wurde. Dieser gibt Aufschlüsse über die Abbildung und dessen Kontext. Sei da ruhig etwas größzüger mit der Relevanz sofern der Kontext wirklich darauf hindeutet, dass das Bild relevant ist – ein relevantes Keyword oder der Wortstamm sollte im umliegenden text aber schon vorkommen. Wobei schlichte Abbildungen oder Grafik, wie ein Logo aber auch ein Bild, dass vermutlich eher als Mood Image gesetzt wurde, weniger relevant ist. 

Am Ende gibst Du immer einen Score ab, der beschreibt, wie relevant die Abbildung für die Beantwortung der Frage war. der Score reicht von 0 (nicht relevant) bis 1 (absolut relevant). Sei dabei differenziert und traue dich auch Werte zwischen 0 und 1 abzugeben (z.B. 0.3, 0.4, 0.6, 0.78 etc.).
Sollten die Abbildung nur einen Teil der Frage oder zumindest einen Aspekt der Frage beantworten, dass sollte dies bereits einen sehr hohen Score geben. Wenn das Bild Aspekte behandelt nach denen NICHT gefragt wurde, sollt eder Score niedrig ausfallen. Wenn ein relevantes Keyword im Kontext enthalten ist sollte der Wert mindestens 0.8 sein! 
Den Score, gibst Du in folgendem String aus: 'SCORE="<score>"'`

/*
const tableSystemPrompt = `Du analysierst Tabellen und gibst eine prezise und gut strukturierte Antwort auf die Frage mit zusätzlichen Informationen aus der Tabelle, die die Frage berühren könnten. 
Du greifst nicht auf Dein wissen zurück und antwortest AUSSCHLIEßLICH auf basis der Tabelle.
Sei extrem kritisch. Wenn z.B. nach DN gefragt wird, es in der Tabelle aber um WN oder HN geht, dann scheint die Tabelle die Frage nicht zu beantworten. 
Du gibst einfach trocken die relevanten informationen wieder ohne dabei zu spekulieren oder Voreilige Schlüsse zu ziehen! Außerdem gibst den Kontext, auf den sich Werte und Informationen beziehen wieder (geht es um DN, WN/HN Baulänge etc.). Das Du die Werte wie besispielsweise DN, WN/HN, Wanddicke etc. in Deiner Antwort nennst ist essentiell! 
Formatiere deine Anwtort als Markdown. Bevorzugt bei numerischen Daten als Liste.

Überprüfe die Zeilenübergänge in den Tabelle ganz genau und sei Dir sicher, welche Werte zu welchen gehören. 

Für die Analysie der Abbdildung ist es wichtig, dass Du den Kontext beachtest, der unten beigefügt wurde. Dieser gibt Aufschlüsse über den Geltungsbereich der Informationen der Tabelle. Gib diese in Deiner Antwort immer wieder. Also rücke die Informationen in den Kontext, der hier beigefügt wurde.
Dabei kann es durchaus sein, dass die Antwort gar nicht in der Tabelle zufinden ist. Wenn dies der Fall ist, dann sag, dass diese Tabelle keine Antwort auf die Frage gibt (z.B. wenn nach DN gefragt wird, die Tabelle aber nur Aufschluss über WN/HN gibt). 

Am Ende gibst Du immer einen Score ab, der beschreibt, wie relevant die Abbildung für die Beantwortung der Frage war. der Score reicht von 0 (nicht relevant) bis 1 (absolut relevant). Sei dabei differenziert und traue dich auch Werte zwischen 0 und 1 abzugeben (z.B. 0.3, 0.4, 0.6, 0.78 etc.).
Sollten die Abbildung nur einen Teil der Frage oder zumindest einen Aspekt der Frage beantworten, dass sollte dies bereits einen sehr hohen Score geben. Wenn das Bild Aspekte behandelt nach denen NICHT gefragt wurde, sollt eder Score niedrig ausfallen.
Den Score, gibst Du in folgendem String aus: 'SCORE="<score>"'`
*/


/*
`
Du analysierst Abbildungen/Tabellen und gibst eine prezise und gut strukturierte Antwort auf die Frage mit zusätzlichen Informationen aus der Tabelle, die die Frage berühren könnten. 
Du greifst nicht auf Dein wissen zurück und antwortest AUSSCHLIEßLICH auf Basis der Abbildung bzw. Tabelle.
Sei extrem kritisch. Wenn z.B. nach etwas bestimmten gefragt wirt, es in der Tabelle aber um etwas anderes geht, dann scheint die Tabelle die Frage nicht zu beantworten. 
Du gibst einfach trocken die relevanten informationen wieder ohne dabei zu spekulieren oder Voreilige Schlüsse zu ziehen! Außerdem gibst den Kontext, auf den sich Werte und Informationen beziehen wieder.
Formatiere deine Anwtort als Markdown.

Für die Analyse der Tabelle ist es wichtig, dass Du den Kontext beachtest, der unten beigefügt wurde. Dieser gibt Aufschlüsse über den Geltungsbereich der Informationen der Tabelle. Gib diese in Deiner Antwort immer wieder. Also rücke die Informationen in den Kontext, der hier beigefügt wurde.
Dabei kann es durchaus sein, dass die Antwort gar nicht in der Tabelle zufinden ist. Wenn dies der Fall ist, dann sag, dass diese Tabelle keine Antwort auf die Frage gibt. 

Wenn auf der Abbdilung die Architektur einer Software beschrieben ist, dann verfolge die Linien zwischen den Komponenten genau. Sei Dir sicher, dass Komponenten wirklich miteinander agieren und welche nicht. 

Am Ende gibst Du immer einen Score ab, der beschreibt, wie relevant die Abbildung für die Beantwortung der Frage war. der Score reicht von 0 (nicht relevant) bis 1 (absolut relevant). Sei dabei differenziert und traue dich auch Werte zwischen 0 und 1 abzugeben (z.B. 0.3, 0.4, 0.6, 0.78 etc.).
Sollten die Abbildung nur einen Teil der Frage oder zumindest einen Aspekt der Frage beantworten, dass sollte dies bereits einen sehr hohen Score geben. Wenn das Bild Aspekte behandelt nach denen NICHT gefragt wurde, sollt eder Score niedrig ausfallen.
Den Score, gibst Du in folgendem String aus: 'SCORE="<score>"'
`
*/


/* 

Du analysierst Tabellen und gibst eine prezise und gut strukturierte Antwort auf die Frage mit zusätzlichen Informationen aus der Tabelle, die die Frage berühren könnten. 
Du greifst nicht auf Dein wissen zurück und antwortest AUSSCHLIEßLICH auf basis der Tabelle.
Sei extrem kritisch. Wenn z.B. nach DN gefragt wird, es in der Tabelle aber um WN oder HN geht, dann scheint die Tabelle die Frage nicht zu beantworten. 
Du gibst einfach trocken die relevanten informationen wieder ohne dabei zu spekulieren oder Voreilige Schlüsse zu ziehen! Außerdem gibst den Kontext, auf den sich Werte und Informationen beziehen wieder (geht es um DN, WN/HN Baulänge etc.). Das Du die Werte wie besispielsweise DN, WN/HN, Wanddicke etc. in Deiner Antwort nennst ist essentiell! 
Formatiere deine Anwtort als Markdown.

Für die Analysie der Abbdildung ist es wichtig, dass Du den Kontext beachtest, der unten beigefügt wurde. Dieser gibt Aufschlüsse über den Geltungsbereich der Informationen der Tabelle. Gib diese in Deiner Antwort immer wieder. Also rücke die Informationen in den Kontext, der hier beigefügt wurde.
Dabei kann es durchaus sein, dass die Antwort gar nicht in der Tabelle zufinden ist. Wenn dies der Fall ist, dann sag, dass diese Tabelle keine Antwort auf die Frage gibt (z.B. wenn nach DN gefragt wird, die Tabelle aber nur Aufschluss über WN/HN gibt). 

Am Ende gibst Du immer einen Score ab, der beschreibt, wie relevant die Abbildung für die Beantwortung der Frage war. der Score reicht von 0 (nicht relevant) bis 1 (absolut relevant). Sei dabei differenziert und traue dich auch Werte zwischen 0 und 1 abzugeben (z.B. 0.3, 0.4, 0.6, 0.78 etc.).
Sollten die Abbildung nur einen Teil der Frage oder zumindest einen Aspekt der Frage beantworten, dass sollte dies bereits einen sehr hohen Score geben. Wenn das Bild Aspekte behandelt nach denen NICHT gefragt wurde, sollt eder Score niedrig ausfallen.
Den Score, gibst Du in folgendem String aus: 'SCORE="<score>"'

Zusatzinformtaionen:
Bzgl. der Qualitätsrichtlinien gelten folgende Symbole nach DIN 1916, DIN 1201 und die folgenden aufgeführten Symbole (alle Maße in mm):
HN Höhe nominal
WN Weite nominal
b_k Breite der Dichtungskammer auf dem Spitzende
d_1 Innendurchmesser
d_so Innendurchmesser der Muffe im Abstand l_m von der Muffenstirnfläche
d_sp Außendurchmesser des Spitzendes im Abstand l_s vom Spitzendspiegel
d_rs Außendurchmesser des Spitzendes am Punkt l_rs
h_k1 bzw. h_rs (Kammer bzw. Schulter)
h_j Profilhöhe des Dichtringes
h_k1, h_k2 Höhen der Dichtungskammer auf dem Spitzende
h_rs Höhe der Schulter auf dem Spitzende
l_m Abstand der Messstelle in der Muffe für die Bestimmung der Muffenspaltweite (planmäßiger Sitz des Dichtmittels)
l_rs Abstand zwischen Spitzendspiegel und Kammer bzw. Schulter
l_s Abstand der Messstelle für die Messung des Spitzendaußendurchmessers d_sp vom Spitzendspiegel (planmäßiger Sitz des Dichtmittels)
l_so Muffenlänge
l_sp Spitzendlänge
t Wanddicke am Rohrschaft
t_1 Wanddicke im Kämpfer
t_2 Wanddicke im Scheitel
t_3 Wanddicke in der Sohle
w Muffenspaltweite

Außerdem gehört diese Information zu Qualitätsrichtlinie 1-1 Tabelle 3 auf Seite 6:
w = (d_so - d_sp) / 2, dabei sind d_so und d_sp Mittelwerte, die aus den unter Erfassung der Mindest- und Höchstwerte am Rohr gemessenen Werten d_so und d_sp gebildet werden.
a Die Grenzabmaße ergeben sich aus den entsprechenden Maßen der Muffeninnendurchmesser und den Maßen und Grenzabmaßen der Muffenspaltweiten.
b Maße für Betonrohre und Stahlbetonrohre mit unbewehrtem Spitzende.
c Mindestmaße für Stahlbetonrohre mit unbewehrtem Spitzende. 
d Mindestmaße für Stahlbetonrohre mit bewehrtem Spitzende. Mit diesen Maßen dürfen auch Betonrohre und Stahlbetonrohre mit unbewehrtem Spitzende hergestellt werden.
e Die Länge des Spitzendes l_sp sollte mindestens 5 mm länger ausgeführt werden als die Muffenlänge l_so.

*/

/*

	`Schau mal, ich habe das beigefügte Bild. Wie würdest Du die Anordnung der Tabellenzellen beschreiben. Sie ist ja schon unkonventionell. Es hat viele Zeilenübergreifende Werte: Einige Werte erstrecken sich über mehrere Zeilen hinweg.
			Wenn Du siehst, dass eine Zelle sich über mehrere Zeilen erstreckt, dann prüfe genau welche Zeile tatsächlich angrenzt.

Beantworte die Frage und erkläre wie Du auf den Wert gekommen bist
`

| Erste Spalte      | Zweite Spalte                     | Dritte Spalte                     |
|-------------------|-----------------------------------|-----------------------------------|
| A1 				| B1 gehört zu A1                   | D1 gehört zu A1                   |
|                   | B2 gehört zu A1                   | Kein Wert angegeben                    |
|                   | B3 gehört zu A1                   | D2					            |
| A2				| B4 gehört zu A2                   | 	                                |
|                   | B5 gehört zu A2                   |                                   |
|                   | B6 gehört zu A2                   | D3                               	|
| A3				| B7 gehört zu A3                   |                           		|
|                   | B8 gehört zu A3                   |                                   |
|                   | B9 gehört zu A3                   |                                   |
1. Schaue welche(r) Wert(e) als Ausgangswert genutzt werden können. 
			2. Finde den bzw. die Werte in der Tabelle. 
			3. Schaue welche Tabellenzellen an die Zelle(n) angrenzen und entscheide auf Basis dessen, in welcher Tabellenzeile der Wert steht, der zur Beantwortung der Frage relevant ist. 
			Gehe direkt von der ersten relevanten Zelle zur Zielzelle, denn eine Zelle kann in einer Spalte für eine Vielzahl von Zeilen in anderen Spalten gelten. 
			Wenn Du denkst keinen Wert findest, wo Du ihn vermutest, dann schau genau wohin sich die angrenzede Zelle erstreckt und ob da nicht doch ein Wert drinsteht.

////////////////////////
Du hast die Aufgabe Tabellen zu analysieren. Dabei gehst Du wie folgt vor:


Beachte, dass eine Spalte sich in mehrere Spalten unterteilen kann und in der nächsten Spalte wieder für noch mehr Spalten zusammengefasst sein kann. Entscheidend ist welche Tabellenzellen aneinander angrenzen. 
Achte nur auf die Zellen. Nicht auf die Position der Werte innerhalb der Zellen.
EIne Zelle ist ein weißes Feld, dass von schwarzen Linien beschränkt ist.

Entscheidend ist also die Grenze, die die Zellen beschränken und wie weit sich diese berühren.
Entscheidend ist die Zelle der Tabelle und die Position der gesamten Zelle! Nicht die Position des Werts innerhalb der Zelle. 

Tabellenzelle für mehrere Werte horizontal und vertikal zusammengefasst sein kann!
Beachte, dass eine Tabellenzelle für mehrere Werte horizontal und vertikal zusammengefasst sein kann

Achte also primär darauf welche Zellen horizontal an den gesuchten Werten angrenzen.
Ein Wert kann also weiter unten oder oben in der selben Zeile stehen. Wichtig sind die Linien, die die Zellen begrenzen. 
Du beantwortest die Frage und erklärst wie Du auf den Wert gekommen bist

Eine Zelle kann sich über viele Zeilen einer Spalte erstrecken. Achte bei der Analyse nur auf die Zellen. Achte nicht auf die Position der Werte innerhalb der Zellen. 
Ziehe auch in Betracht, dass sich ein Wert in einer Zeile, der Horizontal abweicht richtig sein kann, wenn die Tabellenzelle an den Wert angrenzt. 
Analysiere die Tabelle ganz genau!
*/

// Eine Tabellenzelle kann für mehrere Werte horizontal und vertikal zusammengefasst sein.
// Du schaust genau welche Zellen der Tabelle zueinander gehören.
//und erklärst wie Du auf den Wert gekommen bist
// 1. Du analysierst die Fragestellung und merkst dir die relevanten Bergiffe suchst die Stellen in dem bereitgestellten Bild/der Tabelle raus, die zur Beantwortung der Frage relevant sind

// Gib die Informationen präzise wieder. Mache dabei keine Annhamen und gib nur das wieder, was gesichert gesagt werden kann!

/** Post Endpoint /analyseImage */
app.post("/analyseImage", async (req, res) => {
	console.log("/////////////////////")
	console.log("/analyse image called")
	console.log("req.body.model: ", req.body.model)
	// console.log("req.body.userPrompt", req.body.userPrompt)
	console.log("req.body.type", req.body.type)
	console.log("req.body.imageContext", req.body.imageContext)
	console.log("req.body.imageUrl", req.body.imageUrl)

	// console.log("req.body.model (image analysis): ", req.body.model)

	const imageSystemPromt = req.body.type === "TABLE" ? tableSystemPrompt : ""

	const payload = {
		imageSystemPromt: imageSystemPromt,
		imageContext: req.body.imageContext,
		userPrompt: req.body.userPrompt,
		imageUrl: req.body.imageUrl,
	}
	// console.log("payload from analyseImage: ", payload)

	if (req.body.model === undefined || req.body.model === "gpt-4o" || req.body.model.includes("gpt")) {
		console.log("/// imageAnalysisOpenAi called ///")
		const response = await imageAnalysisOpenAi(payload)
		res.send(response)
	}
	
	
	if (req.body.model.includes("gemini")) {
		const response = await imageAnalysisGemini(payload)
		res.send(response)
	}

	if (req.body.model === "pixtral") {
		const response = await imageAnalysisPixtral(payload)
		res.send(response)
	}
	// console.log("response: ", response)
})

/*
 * GPT 4o
 *
 * Benchmark Frage: Was ist die richtige Muffenspaltweite für Rohre mit einem Spitzendurchmesser von 426?
 *
 */
app.post("/judgeIfImageIsRelevant", async (req, res) => {
	console.log("/analyse image called")
	console.log("req.body.type", req.body.type)
	console.log("req.body.imageContext", req.body.imageContext)
	console.log("req.body.wtf", req.body.wtf)

	const imageSystemPromt = req.body.type === "TABLE" ? tableSystemPrompt : ""

	const payload = {
		imageSystemPromt: imageSystemPromt,
		imageContext: req.body.imageContext,
		userPrompt: req.body.userPrompt,
		imageUrl: req.body.imageUrl,
	}

	const response = await judgeIfImageIsRelevant(payload)
	console.log("response: ", response)
	res.send(response)
})

const judgeImageAnalisSystemPrompt = `Du entscheidest ausschließlich und möglichst schnell darüber, ob das Bild die benötigten informationen enthält. Dabei gibst Du ausschließlich einen score zwischen 0 und 1 zurück (0 ist gar nicht relevant und 1 ist sehr relevant). Sonst nichts!`

async function judgeIfImageIsRelevant(payload) {
	console.log("judgeIfImageIsRelevant called!")
	console.log("payload: ", payload)
	const response = await openai.chat.completions.create({
		// model: "gpt-4o",
		// model: "chatgpt-4o-latest",
		model: "gpt-4o-mini",
		messages: [
			{
				role: "user",
				content: [
					{
						type: "text",
						text: `Anweisung: ${judgeImageAnalisSystemPrompt} ***Fragestellung: ${payload.userPrompt}`,
					},
					{
						type: "image_url",
						image_url: {
							// url: "https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg",
							url: "https://firebasestorage.googleapis.com/v0/b/project-report-f7120.appspot.com/o/newwways-testing-mnpk3%2F9cb1697b-06a8-4523-801c-79539b000000%2F6_k0sv5ws2.jpg?alt=media&token=bcb05bbe-c2f0-4b4b-8b29-6f51537e2e6d",
							// url: payload.imageUrl,
							detail: "high",
						},
					},
				],
			},
		],
		temperature: 0,
	})

	response.choices[0].message.content = `Analyse der Tabelle: ${response.choices[0].message.content}` // response.choices[0].message.content
	// console.log(`response.choices[0].message.content: `, response.choices[0].message.content)

	// console.log("response.choices[0].message.content: ", response.choices[0].message.content)

	return response
}

const testPayload = {
	// userPrompt: "Was ist die Empfehlung für Spitzendurchmesser bei Betonrohren mit Glockenmuffe bei DN 300?",
	userPrompt: "Welche Grenzabmaße von der Parallelität der Stirnfläche darf es bei Rohren mit WN/HN 300/450 geben?",
}
// judgeIfImageIsRelevant(testPayload)

async function imageAnalysisOpenAi(payload) {
	// console.log("payload.imageSystemPromt: ", payload.imageSystemPromt)
	// console.log("payload.imageContext: ", payload.imageContext)
	// console.log("payload from Image analysis: ", payload)
	const response = await openai.chat.completions.create({
		// model: "gpt-4o",
		// model: "chatgpt-4o-latest",
		// model: "gpt-4o-2024-08-06", // bisher bestes Model
		model: "gpt-4o", 
		// model: "gpt-4o-mini",
		messages: [
			{
				role: "user",
				content: [
					{
						type: "text",
						text: `Anweisung: ${payload.imageSystemPromt} \n\n Kontext (Inhalte, die um die Tabelle herum stehen): ${payload.imageContext} \n\n | ***Fragestellung: ${payload.userPrompt}`,
					},
					{
						type: "image_url",
						image_url: {
							url: payload.imageUrl,
							detail: "high",
						},
					},
				],
			},
		],
		temperature: 0,
		// stream: true,
		// stream_options: {include_usage: true},
	})

	// console.log("response: ", response)

	/*
	// const stream = await openai.chat.completions.create(requestObject)

	// create a for loop to read the stream
	// for await (const chunk of stream) {
	// 	console.log("chunk.choices: ", chunk.choices)
	// 	console.log("chunk.usage: ", chunk.usage)
	// }

	// return stream
	*/

	if (response.choices[0].message.content) {
		response.choices[0].message.content = `Analyse der Tabelle: ${response.choices[0].message.content}` // response.choices[0].message.content
		console.log(`response.choices[0].message.content: `, response.choices[0].message.content)
	}
	if (!response.choices[0].message.content) {
		console.log("no content")
	}

	return response
}

async function imageAnalysisGemini(reqBody, useStream = false) {
	console.log("imageAnalysisGemini called")

	const model = reqBody.model
	const prompt =
		reqBody.prompt?.context && reqBody.prompt?.messages
			? `Anweisung: ${reqBody.imageSystemPromt} \n\n Kontext (Inhalte, die um die Tabelle herum stehen): ${reqBody.prompt.context} ***Fragestellung: ${
					reqBody.prompt.messages[0]?.content ?? ""
			  }`
			: `Anweisung: ${reqBody.imageSystemPromt} \n\n Kontext: ${reqBody.imageContext}\n\nFragestellung: ${reqBody.userPrompt}`

	console.log("prompt for Image Analysis: ", prompt)

	// Bild herunterladen
	const imageUrl = reqBody.imageUrl
	if (!imageUrl) throw new Error("imageUrl fehlt im reqBody")

	let base64Image
	try {
		const res = await axios.get(imageUrl, {responseType: "arraybuffer"})
		base64Image = Buffer.from(res.data).toString("base64")
	} catch (err) {
		console.error("Bild konnte nicht geladen werden:", err)
		throw new Error("Bild konnte nicht geladen werden")
	}

	const parts = [
		{text: prompt},
		{
			inlineData: {
				mimeType: "image/jpeg", // ggf. anpassen je nach Bild
				data: base64Image,
			},
		},
	]

	try {
		const controller = new AbortController()
		const timeoutId = setTimeout(() => controller.abort(), 60000)

		const modelClient = model === "gemini-1.5-pro-001" ? geminiPro : geminiFlash

		const result = await modelClient.generateContent({
			contents: [{role: "user", parts}],
			// signal: controller.signal,
		})
		clearTimeout(timeoutId)

		const response = await result.response
		const text = await response.text()
		console.log("Analyse-Ergebnis (non-stream):", text)
		return {
			choices: [
				{
					message: {
						content: text,
					},
				},
			],
		}
	} catch (error) {
		if (error.name === "AbortError") {
			console.log("Gemini Image-Request timed out")
			throw new Error("Gemini Image-Request timed out")
		}
		throw error
	}
}

async function imageAnalysisPixtral(payload) {
	console.log("payload: ", payload)
	console.log("mixtralPixtralApiKey: ", mixtralPixtralApiKey)
	const pixtralRequestObject = {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
			Authorization: `Bearer ${mixtralPixtralApiKey}`,
		},
		body: JSON.stringify({
			model: "pixtral-12b-2409",
			messages: [
				{
					role: "user",
					content: [
						{
							type: "text",
							text: "Welche Muffenspaltweite gibt es für DN300 Rohre?",
						},
						{
							type: "image_url",
							image_url:
								"https://firebasestorage.googleapis.com/v0/b/project-report-f7120.appspot.com/o/newwways-testing-mnpk3%2F9cb1697b-06a8-4523-801c-79539b000000%2F6_k0sv5ws2.jpg?alt=media&token=bcb05bbe-c2f0-4b4b-8b29-6f51537e2e6d",
						},
					],
				},
				{
					role: "assistant",
					content:
						"Tatsächlich gibt es zwei unterschiedliche Muffenspaltweiten. 7,8 +- 1,2 für DN300 mkit einem Spitzendurchmesser von 386mm (c) und 404mm (c). Wohingegen ein DN300 Rohr mit einem Spitzendurchmesser von 426mm (d) hat eine Muffenspaltweite von 9,1 +- 1,2 mm haben sollte.",
				},
				{
					role: "user",
					content: [
						{
							type: "text",
							text: "Welche Muffenspaltweite sollte ein DN600 Rohr haben?",
						},
						{
							type: "image_url",
							image_url:
								"https://firebasestorage.googleapis.com/v0/b/project-report-f7120.appspot.com/o/newwways-testing-mnpk3%2F9cb1697b-06a8-4523-801c-79539b000000%2F6_k0sv5ws2.jpg?alt=media&token=bcb05bbe-c2f0-4b4b-8b29-6f51537e2e6d",
						},
					],
				},
			],
			max_tokens: 300,
		}),
	}

	try {
		const response = await fetch("https://api.mistral.ai/v1/chat/completions", pixtralRequestObject)

		if (!response.ok) {
			throw new Error(`HTTP error! Status: ${response.status}`)
		}

		// Hier wird der Response-Body ausgelesen
		const data = await response.json()
		console.log("Response data from Pixtral:", data)

		// Auf die Antwort-Message zugreifen
		if (data.choices && data.choices.length > 0) {
			const message = data.choices[0].message
			console.log("First choice message:", message)
			return message
		} else {
			console.log("No choices found in the response.")
			return null
		}
	} catch (error) {
		console.error("Error with the Pixtral API request:", error)
		return error
	}
}
// imageAnalysisPixtral("string for testing Pixtral")

/**
 * Google Setup
 */

// Import Auth Data for Google
process.env.GOOGLE_APPLICATION_CREDENTIALS = "project-report-f7120-9dda32ea8083.json"

/**
 * Hier fügen Sie Ihren bestehenden Code für die Google Vertex AI API ein
 */
const project = "project-report-f7120"
const theLocation = "europe-west3"
const aiplatform = require("@google-cloud/aiplatform")

// Imports the Google Cloud Prediction service client
const {PredictionServiceClient} = aiplatform.v1

// Import the helper module for converting arbitrary protobuf.Value objects.
const {helpers} = aiplatform

// Specifies the location of the api endpoint
const clientOptions = {
	apiEndpoint: "us-central1-aiplatform.googleapis.com",
}
const publisher = "google"
const model = "chat-bison@002"

// Instantiates a client
const predictionServiceClient = new PredictionServiceClient(clientOptions)

/** Google **/
async function callPredict(chatData) {
	// Configure the parent resource
	const endpoint = `projects/${project}/locations/${theLocation}/publishers/${publisher}/models/${model}`

	const prompt = chatData

	// console.log("prompt from callPredict function: ", prompt)

	const instanceValue = helpers.toValue(prompt)
	const instances = [instanceValue]

	const parameter = {
		temperature: 0.1,
		maxOutputTokens: 2048, // 512,
		topK: 40,
		topP: 0.95,
	}
	const parameters = helpers.toValue(parameter)

	const request = {
		endpoint,
		instances,
		parameters,
	}

	// Predict request
	try {
		const [response] = await predictionServiceClient.predict(request)
		// console.log("Get chat prompt response")
		const predictions = response.predictions
		// console.log("\tPredictions :")
		// for (const prediction of predictions) {
		// 	console.log(`\t\tPrediction : ${JSON.stringify(prediction)}`)
		// }

		return predictions
	} catch (error) {
		console.error("Ein Fehler ist aufgetreten:", error)
		// Hier kannst du weitere Fehlerbehandlungen durchführen, z.B. eine Antwort an den Client senden oder den Fehler in einer Datenbank loggen.
		const errorMessage = `Error Message: ${error.message}`
		return errorMessage
	}
}

/**
 * init Gemini Setup
 */
const geminiApiKey = "AIzaSyAfPpQ78FFWXnDGd9OqXmb8qxU6fgVcexs"

const {GoogleGenerativeAI} = require("@google/generative-ai")
const {type} = require("os")

// Access your API key as an environment variable (see "Set up your API key" above)
const genAI = new GoogleGenerativeAI(geminiApiKey) // GoogleGenerativeAI(process.env.API_KEY)

const generationConfig = {
	// stopSequences: ["red"],
	maxOutputTokens: 1000000,
	temperature: 0.0,
	topP: 0.0,
	topK: 1,
}
const geminiFlash = genAI.getGenerativeModel({model: "gemini-1.5-flash", generationConfig})
const geminiPro = genAI.getGenerativeModel({model: "gemini-1.5-pro-001", generationConfig})

function formatGeminiRequest(reqBody) {
	console.log("reqBody.prompt from formatGeminiRequest: ", JSON.stringify(reqBody.prompt.messages))
	const {context, messages} = reqBody.prompt
	// console.log("messages from formatGeminiRequest: ", messages)

	// Starte die Historie mit einer Kontextnachricht, wenn Kontext vorhanden
	const composedPrompt = `${context} \n\n ${messages[0].content}`
	const initialContext = {
		role: "user",
		parts: [{text: composedPrompt}],
	}

	const formattedHistory = messages.slice(0, -1).map((msg) => ({
		role: msg.author === "user" ? "user" : "model",
		parts: [{text: msg.content}],
	}))

	// Füge den initialen Kontext hinzu, wenn er definiert ist
	if (context && context.trim() !== "") {
		formattedHistory.unshift(initialContext)
	}

	const lastMsg = messages[messages.length - 1].content

	return {history: formattedHistory, lastMsg}
}

// 6. Schaue ob der Name des Autors/der Autorin im Text steht. Wenn ja, dann schaue ob er weiblich oder männlich ist und passe Herr oder Frau an. Wenn es im gesamten Beitrag KEINEN Namen gibt, der der Autor sein könnte, dann schreibe "sehr geehrte Redaktion <name_der_redaktion>". Keine relevanten Dokumente gefunden. ".
async function runGemini(reqBody, model, composedPrompt, useStream) {
	console.log("runGemini called")
	const {history, lastMsg} = formatGeminiRequest(reqBody)
	console.log("history: ", history)
	console.log("lastMsg: ", lastMsg)

	const prompt = composedPrompt
	// console.log("geminiRun called -> prompt: ", prompt)

	let result
	try {
		const controller = new AbortController()
		const timeoutId = setTimeout(() => controller.abort(), 60000) // 60 Sekunden Timeout

		if (useStream) {
			if (model === "gemini-1.5-flash") {
				streamingResult = await geminiFlash.generateContentStream(prompt)
				return streamingResult
			} else if (model === "gemini-1.5-pro-001") {
				console.log('use 1.5-pro-001 model')
				// streamingResult = await geminiPro.generateContentStream(prompt)
				const chat = await geminiPro.startChat({
					history: history,
					generationConfig: {
						maxOutputTokens: 10000,
					},
				})
				return chat.sendMessageStream(lastMsg)
			}
			// for await (const item of streamingResult.stream) {
			// 	console.log("//////////////// New Line??? /////////////// ")
			// 	console.log("stream chunk: ", JSON.stringify(item))
			// }
			// const aggregatedResponse = await streamingResult.response
			// console.log("aggregated response: ", JSON.stringify(aggregatedResponse))
			// result = aggregatedResponse
			// Return ".response" from gemini response
			
		} else {
			if (model === "gemini-1.5-flash") {
				result = await geminiFlash.generateContent(prompt, {signal: controller.signal})
			} else if (model === "gemini-1.5-pro-001") {
				result = await geminiPro.generateContent(prompt, {signal: controller.signal})
			}
		}

		clearTimeout(timeoutId)

		const response = await result.response
		const text = await response.text()
		console.log("text res from gemini: ", text)

		return text
	} catch (error) {
		if (error.name === "AbortError") {
			console.log("Gemini request timed out")
			throw new Error("Gemini request timed out")
		}
		throw error
	}
}

const openAiModelArray = ["gpt-3.5-turbo-0125", "gpt-4-turbo-preview", "gpt-4", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-4o-2024-08-06"] // gpt-4-1106-preview is only model wich supports seat value for deterministic outputs
const geminiModelArray = ["gemini-1.5-flash", "gemini-1.5-pro-001"]
// Erstellen Sie einen POST-Endpoint
app.post("/predict", async (req, res) => {
	// console.log("/predict called: ", req.body)
	try {
		if (!req.body || !req.body.prompt) {
			return res.status(400).send({error: "No request body or wrong body format"})
		}

		console.log("req.body.model (/predict): ", req.body.model)

		/**
		 * Google models
		 */

		// Palm-2 Bison Chat
		if (req.body.model === "bison") {
			const chatData = req.body.prompt
			console.log("chatData from post request: ", chatData)

			const response = await callPredict(chatData)
			// console.log("response: ", response)
			res.send(response) // Senden Sie die Antwort zurück an den Client
		}

		// Gemini
		if (geminiModelArray.includes(req.body.model)) {
			console.log("gemini called")
			// console.log("req.body.prompt: ", req.body.prompt)

			console.log("req.body.prompt.context: ", req.body.prompt.context)
			console.log("req.body.prompt.context: ", req.body.prompt.context)
			const composedPrompt = `${req.body.prompt.context} ${req.body.prompt.messages[0].content}`
			// console.log("composedPrompt: ", composedPrompt)

			const useStream = true

			if (useStream) {
				// console.log('req from /predict: ', req)
				let stream = await runGemini(req.body, req.body.model, composedPrompt, useStream)
				// console.log("stream: ", stream)
				// console.log("stream.response: ", stream.response)

				// for await (const chunk of stream) {
				// 	console.log('////////////// Chunk retrived in "predict" Endpoint ////////////')
				// 	console.log("chunk: ", chunk)
				// 	process.stdout.write(chunk.choices[0]?.delta?.content || "")
				// 	res.write(chunk.choices[0]?.delta?.content || "")
				// }

				// Das hatte ich bis zum 05.01 aktiv
				for await (const chunk of stream.stream) {
					// console.log('////////////// Chunk retrieved in "predict" Endpoint ////////////')
					// console.log("chunk: ", chunk)
					const content = chunk.text() || "" // Falls dein Stream-Format ein Text ist.
					// console.log("content from gemini stream: ", content)
					res.write(content)
				}
				// console.log("stream finished")

				res.end()
				

				// Set headers once before starting to write
				// res.setHeader("Content-Type", "text/plain")

				// for await (const chunk of stream.stream) {
				// 	const content = chunk.text() || ""
				// 	res.write(content)
				// }
				// res.end()
			} else {
				const response = await runGemini(req.body.model, composedPrompt, useStream)
				// console.log("response from gemini: ", response)

				let jsonResponse
				try {
					jsonResponse = JSON.parse(response)
					// console.log("jsonResponse: ", jsonResponse)
				} catch (error) {
					console.log("Error: ", error)
					jsonResponse = {message: response}
					// console.log("jsonResponse: ", jsonResponse)
				}

				// console.log("jsonResponse to be sent: ", jsonResponse)

				// Setzen des Content-Type Headers
				res.setHeader("Content-Type", "application/json")
				res.send(JSON.stringify(jsonResponse)) // Senden Sie die Antwort zurück an den Client
			}
		}

		if (openAiModelArray.includes(req.body.model)) {
			console.log("model: ", req.body.model)
			// console.log("req.body: ", req.body)

			function transformArrayForOpenAi(originalArray) {
				return originalArray.map((item) => ({
					role: item.author,
					content: item.content,
				}))
			}

			const messagesArray = transformArrayForOpenAi(req.body.prompt.messages)
			// console.log("messagesArray: ", messagesArray)

			const messagesObject = [{role: "system", content: req.body.prompt.context}, ...messagesArray]
			// console.log("messagesObject: ", messagesObject)

			let stream = await gptResponse(req.body.model, messagesObject)
			// console.log("stream: ", stream)

			for await (const chunk of stream) {
				// process.stdout.write(chunk.choices[0]?.delta?.content || "")
				res.write(chunk.choices[0]?.delta?.content || "")
				if (chunk.usage) {
					const usageInfo = JSON.stringify(chunk.usage) // Stelle sicher, dass es ein String ist
					process.stdout.write(usageInfo)
					res.write("[[USAGE_INFO]]")
					res.write(usageInfo)
				}
			}
			res.end()
		}
	} catch (error) {
		res.status(500).send({error: error.message})
	}
})

// simple test Endpoint for gemini
app.post("/testingGemini", async (req, res) => {
	console.log("/testingGemini called: ", req)

	console.log("req.body.model (gemini): ", req.body.model)

	/**
	 * Google models
	 */
	// Palm-2 Bison Chat
	// Gemini
	if (geminiModelArray.includes(req.body.model)) {
		console.log("gemini called")
		// console.log("req.body.prompt: ", req.body.prompt)

		// const composedPrompt = `${req.body.prompt.context} ${req.body.prompt.messages[0].content}`
		// console.log("composedPrompt: ", composedPrompt)

		const response = await runGemini(req.body.model, req.body.prompt)
		res.send(response) // Senden Sie die Antwort zurück an den Client
	}
})

/**
 * request body example
 */
// { "prompt": {
//     "context":"Du bist hilfreiche und unterstützt mich bei der Analyse von Projektberichten. Du hast immer eine gute Antwort. Die Basis unseres Gesprächs ist der folgende Text über das Projekt Testprojekt:Also erstmal; Dienstag habe ich immer meinen langen Tag also das passt das für mich.   Bzgl. dem Entwurf von Tom: Ich hätte designtechnich paar Dinge anzumerken aber wir hatten ihm ja gesagt, dass er da seinen Spielraum bekommt. Unsere Aufgabe war ja der Aufbau also würde ich sagen, solange er sich daran hält, passt das. Bzw. sprechen wir darüber dann ja auch nochmal voraussichtlich am Dienstag."
//     ,"examples":[]
//     ,"messages":[
//         {"author":"user","content":"Wann hat Julian seinen langen Tag?"}
//         ]
//     }
// }

// test Endpoint
app.get("/", async (req, res) => {
	res.send("Hello from chatWithAi.js") // Senden Sie die Antwort zurück an den Client
})

// Starten Sie den Server
const PORT = 6500
const server = app.listen(PORT, () => {
	console.log(`Server läuft auf Port ${PORT}`)
})

server.setTimeout(6000000) // 10 Minuten für Timeout
