const express = require("express")
const cors = require("cors")

const app = express()

app.use(cors()) // Aktiviert CORS für alle Routen
app.use(express.json()) // Um JSON-Body aus Requests zu parsen

// Konfiguration
require("dotenv").config()

/**
 * Open AI Setup
 */
// import OpenAI from "openai"
const OpenAI = require("openai")
const openAIApiKey = process.env.OPENAI_API_KEY

const openai = new OpenAI({
	apiKey: openAIApiKey,
	organization: "org-BLBnq9qzPbbWuPscJxEUdA5R",
})

async function processOpenAIStream(stream, res) {
	for await (const chunk of stream) {
		process.stdout.write(chunk.choices[0]?.delta?.content || "")
		let content = chunk.choices[0]?.delta?.content || ""
		// Verarbeiten Sie den Inhalt wie benötigt, z.B. senden an eine HTTP-Antwort
		res.write(content)
	}
	// Schließen Sie die Response, wenn der Stream endet
	res.end()
}

// TODO: Add Temperature etc to params
async function gptResponse(model, messagesObject) {
	console.log("gptResponse called")
	console.log("messagesObject: ", JSON.stringify(messagesObject))
	// console.log("-> body", body)
	// console.log("-> body.context ", body.context)
	// console.log("-> body.messages.content", body.messages[0].content)
	/*
	const completion = await openai.chat.completions.create({
		messages: [
			{role: "system", content: body.context},
			{role: "user", content: body.messages[0].content},
		],
		model: "gpt-4",
		temperature: 0,
	})
	
	console.log(completion.choices[0])
	return completion.choices[0]
	*/

	const stream = await openai.chat.completions.create({
		// model: "gpt-4",
		model: model, // "gpt-4-turbo-preview",
		// model: "gpt-3.5-turbo-0125",
		// messages: [
		// 	{role: "system", content: body.context},
		// 	{role: "user", content: body.messages[0].content},
		// ],
		messages: messagesObject,
		temperature: 0,
		stream: true,
	})

	/*
	for await (const chunk of stream) {
		// console.log("chunk.choices[0]?.delta?.content: ", chunk.choices[0]?.delta?.content)
		process.stdout.write(chunk.choices[0]?.delta?.content || "")
		// console.log("content: ", content)
		// process.stdout.write(chunk.choices[0]?.delta?.content || "")
	}

	*/
	return stream

	// stream.on("end", () => {
	// 	res.end() // Schließt die Verbindung, wenn der Stream endet
	// })
}

/**
 * Google Setup
 */

// Import Auth Data for Google
process.env.GOOGLE_APPLICATION_CREDENTIALS = "project-report-f7120-9dda32ea8083.json"

/**
 * Hier fügen Sie Ihren bestehenden Code für die Google Vertex AI API ein
 */
const project = "project-report-f7120"
const theLocation = "europe-west3"
const aiplatform = require("@google-cloud/aiplatform")

// Imports the Google Cloud Prediction service client
const {PredictionServiceClient} = aiplatform.v1

// Import the helper module for converting arbitrary protobuf.Value objects.
const {helpers} = aiplatform

// Specifies the location of the api endpoint
const clientOptions = {
	apiEndpoint: "us-central1-aiplatform.googleapis.com",
}
const publisher = "google"
const model = "chat-bison@002"

// Instantiates a client
const predictionServiceClient = new PredictionServiceClient(clientOptions)

async function callPredict(chatData) {
	// Configure the parent resource
	const endpoint = `projects/${project}/locations/${theLocation}/publishers/${publisher}/models/${model}`

	// const prompt = {
	// 	context: "My name is Miles. You are an astronomer, knowledgeable about the solar system.",
	// 	examples: [
	// 		{
	// 			input: {content: "How many moons does Mars have?"},
	// 			output: {
	// 				content: "The planet Mars has two moons, Phobos and Deimos.",
	// 			},
	// 		},
	// 	],
	// 	messages: [
	// 		{
	// 			author: "user",
	// 			content: "How many planets are there in the solar system?",
	// 		},
	// 	],
	// }
	// console.log("chatData from callPredirect function ", chatData)
	const prompt = chatData

	// console.log("prompt from callPredict function: ", prompt)

	const instanceValue = helpers.toValue(prompt)
	const instances = [instanceValue]

	const parameter = {
		temperature: 0.1,
		maxOutputTokens: 2048, // 512,
		topK: 40,
		topP: 0.95,
	}
	const parameters = helpers.toValue(parameter)

	const request = {
		endpoint,
		instances,
		parameters,
	}

	// Predict request
	try {
		const [response] = await predictionServiceClient.predict(request)
		// console.log("Get chat prompt response")
		const predictions = response.predictions
		// console.log("\tPredictions :")
		// for (const prediction of predictions) {
		// 	console.log(`\t\tPrediction : ${JSON.stringify(prediction)}`)
		// }

		return predictions
	} catch (error) {
		console.error("Ein Fehler ist aufgetreten:", error)
		// Hier kannst du weitere Fehlerbehandlungen durchführen, z.B. eine Antwort an den Client senden oder den Fehler in einer Datenbank loggen.
		const errorMessage = `Error Message: ${error.message}`
		return errorMessage
	}

	// Ändern Sie diesen Teil, um die Antwort zurückzugeben
}

/**
 * init Gemini Setup
 */
const geminiApiKey = "AIzaSyAfPpQ78FFWXnDGd9OqXmb8qxU6fgVcexs"

const {GoogleGenerativeAI} = require("@google/generative-ai")

// Access your API key as an environment variable (see "Set up your API key" above)
const genAI = new GoogleGenerativeAI(geminiApiKey) // GoogleGenerativeAI(process.env.API_KEY)

const generationConfig = {
	// stopSequences: ["red"],
	maxOutputTokens: 1000000,
	temperature: 0.01,
	topP: 0.0,
	topK: 3,
}
const geminiFlash = genAI.getGenerativeModel({model: "gemini-1.5-flash", generationConfig})
const geminiPro = genAI.getGenerativeModel({model: "gemini-1.5-pro-001", generationConfig})

// 6. Schaue ob der Name des Autors/der Autorin im Text steht. Wenn ja, dann schaue ob er weiblich oder männlich ist und passe Herr oder Frau an. Wenn es im gesamten Beitrag KEINEN Namen gibt, der der Autor sein könnte, dann schreibe "sehr geehrte Redaktion <name_der_redaktion>". Keine relevanten Dokumente gefunden. ".
async function runGemini(model, composedPrompt) {
	const prompt = composedPrompt
	console.log("geminiRun called -> prompt: ", prompt)

	/*
	let result
	if (model === "gemini-1.5-flash") {
		result = await geminiFlash.generateContent(prompt)
	}

	if (model === "gemini-1.5-pro-001") {
		result = await geminiPro.generateContent(prompt)
	}

	const response = await result.response
	// console.log("rsponse from gemini: ", response)

	const text = await response.text()
	console.log("text res from gemini: ", text)
	// console.log("text res from gemini finished ")

	return text // response
	*/

	let result
	try {
		const controller = new AbortController()
		const timeoutId = setTimeout(() => controller.abort(), 60000) // 60 Sekunden Timeout

		if (model === "gemini-1.5-flash") {
			result = await geminiFlash.generateContent(prompt, {signal: controller.signal})
		} else if (model === "gemini-1.5-pro-001") {
			result = await geminiPro.generateContent(prompt, {signal: controller.signal})
		}

		clearTimeout(timeoutId)

		const response = await result.response
		const text = await response.text()
		console.log("text res from gemini: ", text)

		return text
	} catch (error) {
		if (error.name === "AbortError") {
			console.log("Gemini request timed out")
			throw new Error("Gemini request timed out")
		}
		throw error
	}
}

const openAiModelArray = ["gpt-3.5-turbo-0125", "gpt-4-turbo-preview", "gpt-4", "gpt-4-0125-preview"]
const geminiModelArray = ["gemini-1.5-flash", "gemini-1.5-pro-001"]
// Erstellen Sie einen POST-Endpoint
app.post("/predict", async (req, res) => {
	console.log("/predict called: ", req)
	try {
		if (!req.body || !req.body.prompt) {
			return res.status(400).send({error: "No request body or wrong body format"})
		}

		console.log("req.body.model: ", req.body.model)

		/**
		 * Google models
		 */
		// Palm-2 Bison Chat
		if (req.body.model === "bison") {
			const chatData = req.body.prompt
			console.log("chatData from post request: ", chatData)

			const response = await callPredict(chatData)
			console.log("response: ", response)
			res.send(response) // Senden Sie die Antwort zurück an den Client
		}
		// Gemini
		if (geminiModelArray.includes(req.body.model)) {
			console.log("gemini called")
			// console.log("req.body.prompt: ", req.body.prompt)

			const composedPrompt = `${req.body.prompt.context} ${req.body.prompt.messages[0].content}`
			console.log("composedPrompt: ", composedPrompt)

			const response = await runGemini(req.body.model, composedPrompt)
			console.log("response from gemini: ", response) //// Diese Zeile wird korrekt geloggt . Es ist allerdings nur ein string. könnte hier das Problem liegen?
			// console.log("response.text() from gemini: ", response.text()) //// Diese Zeile wird korrekt geloggt . Es ist allerdings nur ein string. könnte hier das Problem liegen?

			// let jsonResponse = {message: response}
			let jsonResponse
			try {
				jsonResponse = JSON.parse(response)
				// console.log("jsonResponse: ", jsonResponse)
			} catch (error) {
				console.log("Error: ", error)
				jsonResponse = {message: response}
				// console.log("jsonResponse: ", jsonResponse)
			}

			console.log("jsonResponse to be sent: ", jsonResponse)

			// Setzen des Content-Type Headers
			res.setHeader("Content-Type", "application/json")
			res.send(JSON.stringify(jsonResponse)) // Senden Sie die Antwort zurück an den Client
		}

		if (openAiModelArray.includes(req.body.model)) {
			console.log("model: ", req.body.model)
			console.log("req.body: ", req.body)
			/*
			const response = await gptResponse(req.body.prompt)
			console.log("response: ", response)
			res.send(response)
			*/

			// return gptResponse(req.body.prompt)

			function transformArrayForOpenAi(originalArray) {
				return originalArray.map((item) => ({
					role: item.author,
					content: item.content,
				}))
			}

			const messagesArray = transformArrayForOpenAi(req.body.prompt.messages)
			console.log("messagesArray: ", messagesArray)

			const messagesObject = [
				{role: "system", content: req.body.prompt.context},
				// {role: "user", content: req.body.prompt.messages[0].content},
				...messagesArray,
			]

			let stream = await gptResponse(req.body.model, messagesObject)
			console.log("stream: ", stream)

			// if (stream && stream.on) {
			// 	stream.on("data", (data) => {
			// 		console.log("data: ", data)
			// 		// Verarbeite und sende die Daten an den Client
			// 		res.write(data)
			// 	})

			// 	stream.on("end", () => {
			// 		res.end()
			// 	})
			// } else {
			// 	// Handle case where stream is not as expected
			// 	console.error("Received unexpected response type from gptResponse.")
			// 	res.status(500).send("Internal Server Error: Stream handling issue.")
			// }

			for await (const chunk of stream) {
				process.stdout.write(chunk.choices[0]?.delta?.content || "")
				res.write(chunk.choices[0]?.delta?.content || "")
				// chunk.on("data", (data) => {
				// 	console.log("data: ", data)
				// 	// Verarbeite und sende die Daten an den Client
				// 	res.write(data)
				// })

				// chunk.on("end", () => {
				// 	res.end()
				// })
			}
			res.end()
			// const response = await processOpenAIStream(stream, res)
			// return response
		}
	} catch (error) {
		res.status(500).send({error: error.message})
	}
})

// simple test Endpoint for gemini
app.post("/testingGemini", async (req, res) => {
	console.log("/testingGemini called: ", req)

	console.log("req.body.model: ", req.body.model)
	console.log("req.body.model: ", req.body.model)

	/**
	 * Google models
	 */
	// Palm-2 Bison Chat
	// Gemini
	if (geminiModelArray.includes(req.body.model)) {
		console.log("gemini called")
		// console.log("req.body.prompt: ", req.body.prompt)

		// const composedPrompt = `${req.body.prompt.context} ${req.body.prompt.messages[0].content}`
		// console.log("composedPrompt: ", composedPrompt)

		const response = await runGemini(req.body.model, req.body.prompt)
		res.send(response) // Senden Sie die Antwort zurück an den Client
	}
})

/**
 * request body example
 */
// { "prompt": {
//     "context":"Du bist hilfreiche und unterstützt mich bei der Analyse von Projektberichten. Du hast immer eine gute Antwort. Die Basis unseres Gesprächs ist der folgende Text über das Projekt Testprojekt:Also erstmal; Dienstag habe ich immer meinen langen Tag also das passt das für mich.   Bzgl. dem Entwurf von Tom: Ich hätte designtechnich paar Dinge anzumerken aber wir hatten ihm ja gesagt, dass er da seinen Spielraum bekommt. Unsere Aufgabe war ja der Aufbau also würde ich sagen, solange er sich daran hält, passt das. Bzw. sprechen wir darüber dann ja auch nochmal voraussichtlich am Dienstag."
//     ,"examples":[]
//     ,"messages":[
//         {"author":"user","content":"Wann hat Julian seinen langen Tag?"}
//         ]
//     }
// }

// test Endpoint
app.get("/", async (req, res) => {
	res.send("Hello world") // Senden Sie die Antwort zurück an den Client
})

// Starten Sie den Server
const PORT = 6500
const server = app.listen(PORT, () => {
	console.log(`Server läuft auf Port ${PORT}`)
})

server.setTimeout(6000000) // 10 Minuten für Timeout
