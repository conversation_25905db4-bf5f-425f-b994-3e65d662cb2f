/**
 * TO<PERSON><PERSON>(developer): Uncomment these variables before running the sample.\
 * (Not necessary if passing values as arguments)
 */
// const project = 'YOUR_PROJECT_ID';
// const location = 'YOUR_PROJECT_LOCATION';

const project = "project-report-f7120"
const location = "europe-west3"
const aiplatform = require("@google-cloud/aiplatform")

// Imports the Google Cloud Prediction service client
const {PredictionServiceClient} = aiplatform.v1

// Import the helper module for converting arbitrary protobuf.Value objects.
const {helpers} = aiplatform

// Specifies the location of the api endpoint
const clientOptions = {
	apiEndpoint: "us-central1-aiplatform.googleapis.com",
}

const publisher = "google"
const model = "text-bison@001"

// Instantiates a client
const predictionServiceClient = new PredictionServiceClient(clientOptions)

async function callPredict() {
	// Configure the parent resource
	const endpoint = `projects/${project}/locations/${location}/publishers/${publisher}/models/${model}`

	const prompt = {
		prompt: `Fasse kurz zusammen wo es Probleme gibt. Im Besonderen in den folgenden Kategorien: "Zusammenarbeit Kunde", "Zusammenarbeit intern" "Zusammenarbeit Dienstleister": \n erstmal ist es schön zu hören, dass er doch mit WP arbeitet und wir dementsprechend auch Plugin Empfehlungen geben können. Dennoch muss ich sagen, dass die Aufgebe inkl. Rahmenbedingungen alles andere als optimal sind. Denn die Seite, die als Vorlage herhalten soll, passt einfach nicht. Tom schrieb ja "Das bedeutet, dass gestalterische Änderungen naturgemäß nicht all zu groß sein können – die Struktur/Aufbau sollten also ähnlich bleiben". Es macht gar keinen Sinn, dass wir uns ein Wireframe ausdenken, um die Struktur vorzugeben, wenn sich die Struktur nicht groß ändern darf.  Ich habe noch gar nicht mit der Konzeption angefangen aber ich weiß jetzt schon, dass die Struktur deutlich abweichen sollte und es einige Elemente, die ich gerne nutzen würde, nicht gibt.

Bzgl. dem weiteren Vorgehen haben wir also ein Dilemma. Entweder werden wir unserem Auftrag nicht gerecht eine gute konzeptionelle Arbeit zu machen oder wir werden dem Auftrag nicht gerecht, dass unsere konzeptionelle Arbeit zu der Vorlage passt.

Dementsprechend gibt es zwei Optionen: 

1. Wir machen das Wireframe so, wie wir es für richtig halten. Da das dann hinten und vorne nicht zu der Vorlage passend wird, müssten wir Tom dann viel Spielraum in der konkreten Umsetzung geben, sodass er dann aus dem Wireframe das macht, was er mit seinen System machen kann bzw. kann er dann schauen wie weit er doch noch das ein oder andere Element erstellt. Ich fände das auch nicht dreist, denn es ist ja nur ein Wireframe und kein Screendesign. Dementsprechend würden wir wirklich nur die Struktur vorgeben und mehr Verantwortung im Kontext der konkreten Umsetzung an Tom delegieren. 

2. Wir versuchen das Endergebnis designtechnisch möglichst konkret mitzubestimmen. Das würde bedeuteten, dass ich wirklich versuchen würde mit den Elementen zu arbeiten, die auf der Seite existieren. Dann ist gut abzuschätzen wie es am Ende aussieht aber wir sind sehr eingeschränkt in der Konzeption. 

Da das Wireframe und die Inhalte unsere primäre Aufgabe sind, würde ich empfehlen, dass wir Option 1 wählen. Das konkrete Endergebnis ist dann nicht so kontrollierbar aber die bessere konzeptionelle Arbeit und der Spielraum den er dann hat, können dazu führen, dass es am Ende besser wird als wenn wir uns von Vornherein einschränken.

`,
	}
	const instanceValue = helpers.toValue(prompt)
	const instances = [instanceValue]

	const parameter = {
		temperature: 0.2,
		maxOutputTokens: 256,
		topP: 0.95,
		topK: 40,
	}
	const parameters = helpers.toValue(parameter)

	const request = {
		endpoint,
		instances,
		parameters,
	}

	// Predict request
	const response = await predictionServiceClient.predict(request)
	console.log("Get text prompt response")
	console.log(JSON.stringify(response[0].predictions[0].structValue.fields.content, null, 2))
}

callPredict()
